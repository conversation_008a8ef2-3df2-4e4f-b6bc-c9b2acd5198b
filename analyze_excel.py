#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析农业新闻周报Excel文件的脚本
"""

import sys
import re
from pathlib import Path

try:
    import pandas as pd
    import openpyxl
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("警告：pandas或openpyxl未安装，将尝试使用基础方法读取文件")

def extract_urls_from_text(text):
    """从文本中提取URL"""
    if not isinstance(text, str):
        return []
    
    # 匹配HTTP/HTTPS URL的正则表达式
    url_pattern = r'https?://[^\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\u2000-\u206f\u2070-\u209f\u20a0-\u20cf\u2100-\u214f\u2150-\u218f\u2190-\u21ff\u2200-\u22ff\u2300-\u23ff\u2400-\u243f\u2440-\u245f\u2460-\u24ff\u2500-\u257f\u2580-\u259f\u25a0-\u25ff\u2600-\u26ff\u2700-\u27bf\u27c0-\u27ef\u27f0-\u27ff\u2800-\u28ff\u2900-\u297f\u2980-\u29ff\u2a00-\u2aff\u2b00-\u2bff\u2c00-\u2c5f\u2c60-\u2c7f\u2c80-\u2cff\u2d00-\u2d2f\u2d30-\u2d7f\u2d80-\u2ddf\u2de0-\u2dff\u2e00-\u2e7f\u2e80-\u2eff\u2f00-\u2fdf\u2ff0-\u2fff\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\u3100-\u312f\u3130-\u318f\u3190-\u319f\u31a0-\u31bf\u31c0-\u31ef\u31f0-\u31ff\u3200-\u32ff\u3300-\u33ff\u3400-\u4dbf\u4dc0-\u4dff\u4e00-\u9fff\ua000-\ua48f\ua490-\ua4cf\ua4d0-\ua4ff\ua500-\ua63f\ua640-\ua69f\ua6a0-\ua6ff\ua700-\ua71f\ua720-\ua7ff\ua800-\ua82f\ua830-\ua83f\ua840-\ua87f\ua880-\ua8df\ua8e0-\ua8ff\ua900-\ua92f\ua930-\ua95f\ua960-\ua97f\ua980-\ua9df\ua9e0-\ua9ff\uaa00-\uaa5f\uaa60-\uaa7f\uaa80-\uaadf\uaae0-\uaaff\uab00-\uab2f\uab30-\uab6f\uab70-\uabbf\uabc0-\uabff\uac00-\ud7af\ud7b0-\ud7ff\uf900-\ufaff\ufb00-\ufb4f\ufb50-\ufdff\ufe00-\ufe0f\ufe10-\ufe1f\ufe20-\ufe2f\ufe30-\ufe4f\ufe50-\ufe6f\ufe70-\ufeff\uff00-\uffef\)\(\]\[\}\{\"\'\s]*'
    
    urls = re.findall(url_pattern, text)
    return [url.rstrip('.,;:!?()[]{}"\' ') for url in urls]

def analyze_excel_with_pandas(file_path):
    """使用pandas分析Excel文件"""
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"Excel文件包含以下工作表：{excel_file.sheet_names}")
        
        all_data = {}
        all_urls = []
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n正在分析工作表：{sheet_name}")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"工作表 '{sheet_name}' 的结构：")
            print(f"- 行数：{len(df)}")
            print(f"- 列数：{len(df.columns)}")
            print(f"- 列名：{list(df.columns)}")
            
            # 显示前几行数据
            print(f"- 前5行数据预览：")
            print(df.head().to_string())
            
            all_data[sheet_name] = df
            
            # 从所有单元格中提取URL
            for column in df.columns:
                for value in df[column].dropna():
                    urls = extract_urls_from_text(str(value))
                    all_urls.extend(urls)
        
        return all_data, list(set(all_urls))  # 去重
        
    except Exception as e:
        print(f"使用pandas读取文件时出错：{e}")
        return None, []

def analyze_excel_basic(file_path):
    """基础方法分析Excel文件（不使用pandas）"""
    try:
        from openpyxl import load_workbook
        
        workbook = load_workbook(file_path, data_only=True)
        print(f"Excel文件包含以下工作表：{workbook.sheetnames}")
        
        all_urls = []
        
        for sheet_name in workbook.sheetnames:
            print(f"\n正在分析工作表：{sheet_name}")
            worksheet = workbook[sheet_name]
            
            print(f"工作表 '{sheet_name}' 的结构：")
            print(f"- 最大行数：{worksheet.max_row}")
            print(f"- 最大列数：{worksheet.max_column}")
            
            # 显示前几行数据
            print(f"- 前5行数据预览：")
            for row in range(1, min(6, worksheet.max_row + 1)):
                row_data = []
                for col in range(1, worksheet.max_column + 1):
                    cell_value = worksheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value is not None else "")
                print(f"  行{row}: {row_data}")
            
            # 从所有单元格中提取URL
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value:
                        urls = extract_urls_from_text(str(cell.value))
                        all_urls.extend(urls)
        
        return list(set(all_urls))  # 去重
        
    except Exception as e:
        print(f"使用基础方法读取文件时出错：{e}")
        return []

def main():
    file_path = "农业新闻周报地址.xlsx"

    print(f"检查文件是否存在：{file_path}")
    if not Path(file_path).exists():
        print(f"错误：文件 '{file_path}' 不存在")
        # 列出当前目录的文件
        print("当前目录的文件：")
        for f in Path(".").iterdir():
            print(f"  {f.name}")
        return

    print(f"正在分析文件：{file_path}")
    print("=" * 50)

    print(f"pandas可用性：{PANDAS_AVAILABLE}")

    try:
        if PANDAS_AVAILABLE:
            data, urls = analyze_excel_with_pandas(file_path)
        else:
            urls = analyze_excel_basic(file_path)

        print("\n" + "=" * 50)
        print(f"提取到的URL总数：{len(urls)}")
        print("\n提取到的URL列表：")
        for i, url in enumerate(urls, 1):
            print(f"{i}. {url}")
    except Exception as e:
        print(f"分析过程中出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Excel文件读取器
"""

import sys
import re
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def extract_urls_from_text(text):
    """从文本中提取URL"""
    if not isinstance(text, str):
        return []
    
    # 匹配HTTP/HTTPS URL的正则表达式
    url_pattern = r'https?://[^\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\u2000-\u206f\u2070-\u209f\u20a0-\u20cf\u2100-\u214f\u2150-\u218f\u2190-\u21ff\u2200-\u22ff\u2300-\u23ff\u2400-\u243f\u2440-\u245f\u2460-\u24ff\u2500-\u257f\u2580-\u259f\u25a0-\u25ff\u2600-\u26ff\u2700-\u27bf\u27c0-\u27ef\u27f0-\u27ff\u2800-\u28ff\u2900-\u297f\u2980-\u29ff\u2a00-\u2aff\u2b00-\u2bff\u2c00-\u2c5f\u2c60-\u2c7f\u2c80-\u2cff\u2d00-\u2d2f\u2d30-\u2d7f\u2d80-\u2ddf\u2de0-\u2dff\u2e00-\u2e7f\u2e80-\u2eff\u2f00-\u2fdf\u2ff0-\u2fff\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\u3100-\u312f\u3130-\u318f\u3190-\u319f\u31a0-\u31bf\u31c0-\u31ef\u31f0-\u31ff\u3200-\u32ff\u3300-\u33ff\u3400-\u4dbf\u4dc0-\u4dff\u4e00-\u9fff\ua000-\ua48f\ua490-\ua4cf\ua4d0-\ua4ff\ua500-\ua63f\ua640-\ua69f\ua6a0-\ua6ff\ua700-\ua71f\ua720-\ua7ff\ua800-\ua82f\ua830-\ua83f\ua840-\ua87f\ua880-\ua8df\ua8e0-\ua8ff\ua900-\ua92f\ua930-\ua95f\ua960-\ua97f\ua980-\ua9df\ua9e0-\ua9ff\uaa00-\uaa5f\uaa60-\uaa7f\uaa80-\uaadf\uaae0-\uaaff\uab00-\uab2f\uab30-\uab6f\uab70-\uabbf\uabc0-\uabff\uac00-\ud7af\ud7b0-\ud7ff\uf900-\ufaff\ufb00-\ufb4f\ufb50-\ufdff\ufe00-\ufe0f\ufe10-\ufe1f\ufe20-\ufe2f\ufe30-\ufe4f\ufe50-\ufe6f\ufe70-\ufeff\uff00-\uffef\)\(\]\[\}\{\"\'\s]*'
    
    urls = re.findall(url_pattern, text)
    return [url.rstrip('.,;:!?()[]{}"\' ') for url in urls]

def read_excel_as_zip(file_path):
    """将Excel文件作为ZIP文件读取并提取文本内容"""
    print(f"正在读取Excel文件：{file_path}")
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            print(f"Excel文件内部结构：")
            for name in zip_file.namelist():
                print(f"  {name}")
            
            # 读取共享字符串
            shared_strings = []
            try:
                with zip_file.open('xl/sharedStrings.xml') as f:
                    content = f.read().decode('utf-8')
                    root = ET.fromstring(content)
                    for si in root.findall('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}si'):
                        text_elem = si.find('.//{http://schemas.openxmlformats.org/spreadsheetml/2006/main}t')
                        if text_elem is not None and text_elem.text:
                            shared_strings.append(text_elem.text)
                print(f"共享字符串数量：{len(shared_strings)}")
            except KeyError:
                print("没有找到共享字符串文件")
            
            # 读取工作表
            worksheet_files = [name for name in zip_file.namelist() if name.startswith('xl/worksheets/')]
            print(f"找到工作表文件：{worksheet_files}")
            
            all_text = []
            all_text.extend(shared_strings)
            
            for ws_file in worksheet_files:
                try:
                    with zip_file.open(ws_file) as f:
                        content = f.read().decode('utf-8')
                        root = ET.fromstring(content)
                        
                        # 提取所有文本内容
                        for elem in root.iter():
                            if elem.text and elem.text.strip():
                                all_text.append(elem.text.strip())
                except Exception as e:
                    print(f"读取工作表 {ws_file} 时出错：{e}")
            
            print(f"提取到的文本片段总数：{len(all_text)}")
            
            # 显示前10个文本片段
            print("前10个文本片段：")
            for i, text in enumerate(all_text[:10]):
                print(f"  {i+1}. {text}")
            
            # 提取URL
            all_urls = []
            for text in all_text:
                urls = extract_urls_from_text(text)
                all_urls.extend(urls)
            
            return list(set(all_urls))  # 去重
            
    except Exception as e:
        print(f"读取Excel文件时出错：{e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    file_path = "农业新闻周报地址.xlsx"
    
    print(f"检查文件是否存在：{file_path}")
    if not Path(file_path).exists():
        print(f"错误：文件 '{file_path}' 不存在")
        print("当前目录的文件：")
        for f in Path(".").iterdir():
            print(f"  {f.name}")
        return
    
    print("=" * 50)
    
    urls = read_excel_as_zip(file_path)
    
    print("\n" + "=" * 50)
    print(f"提取到的URL总数：{len(urls)}")
    print("\n提取到的URL列表：")
    for i, url in enumerate(urls, 1):
        print(f"{i}. {url}")

if __name__ == "__main__":
    main()
